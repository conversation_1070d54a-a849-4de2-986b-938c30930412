from fastapi import FastAPI
from pydantic import BaseModel
from typing import List, Optional

# 创建FastAPI应用实例
app = FastAPI(
    title="简单的FastAPI项目",
    description="一个基本的FastAPI示例项目",
    version="1.0.0"
)

# 数据模型
class Item(BaseModel):
    id: Optional[int] = None
    name: str
    description: Optional[str] = None
    price: float
    is_available: bool = True

class ItemCreate(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    is_available: bool = True

# 模拟数据库
items_db = [
    {"id": 1, "name": "苹果", "description": "新鲜的红苹果", "price": 5.0, "is_available": True},
    {"id": 2, "name": "香蕉", "description": "黄色香蕉", "price": 3.0, "is_available": True},
]

# 根路径
@app.get("/")
async def root():
    return {"message": "欢迎使用FastAPI项目！"}

# 获取所有商品
@app.get("/items", response_model=List[Item])
async def get_items():
    return items_db

# 根据ID获取商品
@app.get("/items/{item_id}", response_model=Item)
async def get_item(item_id: int):
    for item in items_db:
        if item["id"] == item_id:
            return item
    return {"error": "商品未找到"}

# 创建新商品
@app.post("/items", response_model=Item)
async def create_item(item: ItemCreate):
    new_id = max([i["id"] for i in items_db]) + 1 if items_db else 1
    new_item = {
        "id": new_id,
        "name": item.name,
        "description": item.description,
        "price": item.price,
        "is_available": item.is_available
    }
    items_db.append(new_item)
    return new_item

# 更新商品
@app.put("/items/{item_id}", response_model=Item)
async def update_item(item_id: int, item: ItemCreate):
    for i, existing_item in enumerate(items_db):
        if existing_item["id"] == item_id:
            updated_item = {
                "id": item_id,
                "name": item.name,
                "description": item.description,
                "price": item.price,
                "is_available": item.is_available
            }
            items_db[i] = updated_item
            return updated_item
    return {"error": "商品未找到"}

# 删除商品
@app.delete("/items/{item_id}")
async def delete_item(item_id: int):
    for i, item in enumerate(items_db):
        if item["id"] == item_id:
            deleted_item = items_db.pop(i)
            return {"message": f"商品 '{deleted_item['name']}' 已删除"}
    return {"error": "商品未找到"}

# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
