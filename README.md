# 简单的FastAPI项目

这是一个基于Python FastAPI框架的简单Web API项目，提供了基本的CRUD操作。

## 功能特性

- ✅ RESTful API设计
- ✅ 数据验证（使用Pydantic）
- ✅ 自动生成API文档
- ✅ 异步支持
- ✅ 基本的CRUD操作

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
python main.py
```

或者使用uvicorn直接运行：

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 访问应用

- 应用地址: http://localhost:8000
- API文档: http://localhost:8000/docs
- 替代文档: http://localhost:8000/redoc

## API端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/` | 欢迎页面 |
| GET | `/items` | 获取所有商品 |
| GET | `/items/{item_id}` | 根据ID获取商品 |
| POST | `/items` | 创建新商品 |
| PUT | `/items/{item_id}` | 更新商品 |
| DELETE | `/items/{item_id}` | 删除商品 |
| GET | `/health` | 健康检查 |

## 示例请求

### 创建商品
```bash
curl -X POST "http://localhost:8000/items" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "橙子",
       "description": "新鲜橙子",
       "price": 4.5,
       "is_available": true
     }'
```

### 获取所有商品
```bash
curl -X GET "http://localhost:8000/items"
```

## 项目结构

```
.
├── main.py              # 主应用文件
├── requirements.txt     # 依赖包列表
└── README.md           # 项目说明
```

## 技术栈

- **FastAPI**: 现代、快速的Web框架
- **Pydantic**: 数据验证和设置管理
- **Uvicorn**: ASGI服务器
